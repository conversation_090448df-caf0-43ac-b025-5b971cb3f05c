<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ESP32-S3 Color Matcher</title>
    <link rel="stylesheet" href="style.css" />
</head>
<body>
    <div class="container">
        <h1>ESP32-S3 Color Matcher</h1>
         
        <div class="control-buttons">
            <button id="led-button" class="off">Toggle LED</button>
            <button id="scan-button">Start Scan</button>
            <button id="white-balance-button">White Balance</button>
        </div>
        <p id="scan-status"></p>

        <div class="card">
            <div class="color-display">
                <div class="color-card">
                    <h2>Measured Color</h2>
                    <div id="measured-swatch" class="swatch"></div>
                    <div class="details"><p id="measured-rgb">RGB: (..., ..., ...)</p></div>
                </div>
                <div class="color-card">
                    <h2>Closest Dulux Match</h2>
                    <div id="matched-swatch" class="swatch"></div>
                    <div class="details">
                        <p id="matched-name">Scanning...</p>
                        <p id="matched-rgb">RGB: (..., ..., ...)</p>
                    </div>
                </div>
            </div>
            <div class="stats">
                <p id="delta-e">Delta E: --</p>
                <p id="confidence">Confidence: N/A</p>
            </div>
        </div>

        <div class="card">
            <div class="collapsible-header">Advanced Details</div>
            <div class="collapsible-content">
                <div class="live-data-grid">
                    <div><p>XYZ: <span id="live-xyz">--</span></p></div>
                    <div><p>Lab: <span id="live-lab">--</span></p></div>
                    <div><p>IR1/IR2: <span id="live-ir">--</span></p></div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="collapsible-header">Sensor Calibration</div>
            <div class="collapsible-content">
                <form id="calib-form">
                    <div class="form-grid">
                        <div>
                            <label for="calib-gain">ALS Gain</label>
                            <select id="calib-gain" name="gain">
                                <option value="1">1x</option><option value="4">4x</option>
                                <option value="16">16x</option><option value="64">64x</option>
                            </select>
                        </div>
                        <div>
                            <label for="calib-ir">IR Compensation Factor</label>
                            <input type="number" id="calib-ir" name="ir_comp" step="0.01" min="0" max="1" />
                        </div>
                        <div>
                            <label for="calib-norm">sRGB Normalization</label>
                            <input type="number" id="calib-norm" name="norm" step="1000" min="1000" max="100000" />
                        </div>
                    </div>
                    <div class="form-actions">
                        <p id="save-status"></p>
                        <button type="submit">Apply &amp; Save</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- New Advanced Settings Card -->
        <div class="card">
            <div class="collapsible-header">Advanced Settings</div>
            <div class="collapsible-content">
                <div class="tabs">
                    <div class="tab active" data-tab="sensor-settings">Sensor Settings</div>
                    <div class="tab" data-tab="color-preview">Live Preview</div>
                </div>
                 
                <div class="tab-content active" id="sensor-settings">
                    <form id="advanced-settings-form">
                        <div class="setting-group">
                            <h3>Sensor Configuration</h3>
                             
                            <div>
                                <label for="integration-time">Integration Time</label>
                                <select id="integration-time" name="integration_time">
                                    <option value="0x01">2.78ms (Fastest)</option>
                                    <option value="0x11">50ms (Balanced)</option>
                                    <option value="0x23">100ms (Accurate)</option>
                                    <option value="0x40">181ms (High Precision)</option>
                                    <option value="0xFF">711ms (Maximum)</option>
                                </select>
                                <div class="setting-description">
                                    Controls how long the sensor collects light. Longer times provide more accurate readings in low light but slower response.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Bright conditions: 2.78ms - 50ms</li>
                                        <li>Normal indoor lighting: 50ms - 100ms</li>
                                        <li>Low light conditions: 181ms - 711ms</li>
                                    </ul>
                                </div>
                            </div>
                             
                            <div>
                                <label for="advanced-gain">ALS Gain</label>
                                <select id="advanced-gain" name="gain">
                                    <option value="1">1x</option>
                                    <option value="4">4x</option>
                                    <option value="16">16x</option>
                                    <option value="64">64x</option>
                                </select>
                                <div class="setting-description">
                                    Amplifies the sensor signal. Higher gain improves sensitivity in low light but may cause saturation in bright conditions.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Direct sunlight: 1x</li>
                                        <li>Bright indoor lighting: 4x</li>
                                        <li>Normal indoor lighting: 16x</li>
                                        <li>Dim lighting: 64x</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                         
                        <div class="setting-group">
                            <h3>Color Processing</h3>
                             
                            <div>
                                <label for="advanced-ir-comp">IR Compensation Factor</label>
                                <input type="number" id="advanced-ir-comp" name="ir_comp" step="0.01" min="0" max="1" />
                                <div class="error-message" id="ir-comp-error">Value must be between 0 and 1</div>
                                <div class="setting-description">
                                    Reduces infrared light interference. Higher values remove more IR influence, improving color accuracy under artificial lighting.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Natural daylight: 0.01 - 0.03</li>
                                        <li>LED lighting: 0.02 - 0.05</li>
                                        <li>Fluorescent lighting: 0.03 - 0.07</li>
                                        <li>Incandescent lighting: 0.05 - 0.10</li>
                                    </ul>
                                </div>
                            </div>
                             
                            <div>
                                <label for="advanced-norm">sRGB Normalization Factor</label>
                                <input type="number" id="advanced-norm" name="norm" step="1000" min="1000" max="100000" />
                                <div class="error-message" id="norm-error">Value must be between 1,000 and 100,000</div>
                                <div class="setting-description">
                                    Scales raw sensor values to sRGB color space. Lower values make colors appear brighter, higher values make them darker.
                                </div>
                                <div class="recommended-values">
                                    <h4>Recommended Values:</h4>
                                    <ul>
                                        <li>Low light: 5,000 - 10,000</li>
                                        <li>Normal lighting: 10,000 - 20,000</li>
                                        <li>Bright lighting: 20,000 - 40,000</li>
                                    </ul>
                                </div>
                            </div>
                             
                            <div>
                                <label for="adaptive-scaling">Adaptive Scaling</label>
                                <select id="adaptive-scaling" name="adaptive_scaling">
                                    <option value="true">Enabled</option>
                                    <option value="false">Disabled</option>
                                </select>
                                <div class="setting-description">
                                    When enabled, automatically adjusts color scaling based on light intensity. Improves performance across varying lighting conditions.
                                </div>
                            </div>
                        </div>
                         
                        <div class="form-actions">
                            <p id="advanced-save-status"></p>
                            <button type="button" id="cancel-button">Cancel</button>
                            <button type="submit">Apply &amp; Save</button>
                        </div>
                    </form>
                </div>
                 
                <div class="tab-content" id="color-preview">
                    <h3>Live Settings Preview</h3>
                    <p>Adjust settings to see their effect on color detection in real-time.</p>
                     
                    <div class="preview-container">
                        <div class="preview-box">
                            <h4>Current Settings</h4>
                            <div id="current-preview" class="preview-swatch"></div>
                            <div class="preview-data">
                                <p>RGB: <span id="current-rgb">--</span></p>
                                <p>XYZ: <span id="current-xyz">--</span></p>
                            </div>
                        </div>
                        <div class="preview-box">
                            <h4>New Settings</h4>
                            <div id="new-preview" class="preview-swatch"></div>
                            <div class="preview-data">
                                <p>RGB: <span id="new-rgb">--</span></p>
                                <p>XYZ: <span id="new-xyz">--</span></p>
                            </div>
                        </div>
                    </div>
                     
                    <div class="nav-buttons">
                        <button type="button" id="back-to-settings">Back to Settings</button>
                        <button type="button" id="apply-preview">Apply These Settings</button>
                    </div>
                </div>
            </div>
        </div>
         
        <footer>ESP32PROS3 &amp; TCS3430</footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const collapsibles = document.querySelectorAll('.collapsible-header');
            collapsibles.forEach(header => {
                header.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const content = this.nextElementSibling;
                    if (content.style.maxHeight) {
                        content.style.maxHeight = null;
                    } else {
                        content.style.maxHeight = content.scrollHeight + "px";
                    }
                });
            });

            const calibForm = document.getElementById('calib-form');
            calibForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                const saveStatus = document.getElementById('save-status');
                saveStatus.textContent = 'Saving...';
                
                fetch('/set_calibration', {
                    method: 'POST',
                    body: new URLSearchParams(formData)
                }).then(response => {
                    if(response.ok) return response.text();
                    throw new Error('Save failed.');
                }).then(text => {
                    saveStatus.textContent = text;
                    setTimeout(() => saveStatus.textContent = '', 3000);
                }).catch(error => {
                    saveStatus.textContent = 'Error!';
                    setTimeout(() => saveStatus.textContent = '', 3000);
                });
            });

            const ledButton = document.getElementById('led-button');
            const scanButton = document.getElementById('scan-button');
            const scanStatus = document.getElementById('scan-status');
            const whiteBalanceButton = document.getElementById('white-balance-button');

            ledButton.addEventListener('click', () => {
                fetch('/toggle_led', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        updateLedButton(data.led_state);
                    })
                    .catch(error => console.error('Error toggling LED:', error));
            });

            scanButton.addEventListener('click', () => {
                const isScanning = scanButton.classList.contains('stop');
                if (!isScanning) {
                    scanStatus.textContent = 'Scanning...';
                    scanButton.textContent = 'Stop Scan';
                    scanButton.classList.add('stop');
                    fetch('/start_scan', { method: 'POST' })
                        .then(response => response.text())
                        .then(text => console.log('Scan started:', text))
                        .catch(error => {
                            scanStatus.textContent = 'Error starting scan!';
                            scanButton.textContent = 'Start Scan';
                            scanButton.classList.remove('stop');
                            console.error('Error starting scan:', error);
                        });
                } else {
                    scanStatus.textContent = 'Scan stopped';
                    scanButton.textContent = 'Start Scan';
                    scanButton.classList.remove('stop');
                    fetch('/stop_scan', { method: 'POST' })
                        .then(response => response.text())
                        .then(text => console.log('Scan stopped:', text))
                        .catch(error => {
                            scanStatus.textContent = 'Error stopping scan!';
                            console.error('Error stopping scan:', error);
                        });
                }
            });

            whiteBalanceButton.addEventListener('click', () => {
                const originalText = whiteBalanceButton.textContent;
                whiteBalanceButton.textContent = 'Calibrating...';
                whiteBalanceButton.disabled = true;

                fetch('/white_balance', { method: 'POST' })
                    .then(response => response.text())
                    .then(text => {
                        scanStatus.textContent = text;
                        setTimeout(() => scanStatus.textContent = '', 3000);
                    })
                    .catch(error => {
                        scanStatus.textContent = 'White balance calibration failed!';
                        setTimeout(() => scanStatus.textContent = '', 3000);
                        console.error('Error during white balance calibration:', error);
                    })
                    .finally(() => {
                        whiteBalanceButton.textContent = originalText;
                        whiteBalanceButton.disabled = false;
                    });
            });

            function updateLedButton(state) {
                ledButton.textContent = `LED: ${state ? 'ON' : 'OFF'}`;
                ledButton.className = state ? 'on' : 'off';
            }

            let isFirstFetch = true;
            function updateUI(data) {
                if (!data) return;

                if(data.data_ready){
                    document.getElementById('measured-swatch').style.backgroundColor = `rgb(${data.measured_r}, ${data.measured_g}, ${data.measured_b})`;
                    document.getElementById('measured-rgb').innerText = `RGB: (${data.measured_r}, ${data.measured_g}, ${data.measured_b})`;
                    document.getElementById('matched-swatch').style.backgroundColor = `rgb(${data.matched_r}, ${data.matched_g}, ${data.matched_b})`;
                    document.getElementById('matched-name').innerText = data.matched_name;
                    document.getElementById('matched-rgb').innerText = `RGB: (${data.matched_r}, ${data.matched_g}, ${data.matched_b})`;
                    document.getElementById('delta-e').innerText = `Delta E: ${data.delta_e.toFixed(2)}`;
                    const confidenceElem = document.getElementById('confidence');
                    confidenceElem.innerText = `Confidence: ${data.confidence}`;
                    confidenceElem.className = data.confidence.toLowerCase();
                    
                    document.getElementById('live-xyz').innerText = `${data.avg_x.toFixed(1)}, ${data.avg_y.toFixed(1)}, ${data.avg_z.toFixed(1)}`;
                    document.getElementById('live-lab').innerText = `${data.avg_l.toFixed(1)}, ${data.avg_a.toFixed(1)}, ${data.avg_b.toFixed(1)}`;
                    document.getElementById('live-ir').innerText = `${data.avg_ir1.toFixed(1)} / ${data.avg_ir2.toFixed(1)}`;

                    // Update scan status
                    if (!data.is_scanning && scanButton.classList.contains('stop')) {
                        scanStatus.textContent = data.delta_e <= 5.0 ? 'Scan Complete: Confident Match!' : 'Scan stopped';
                        scanButton.textContent = 'Start Scan';
                        scanButton.classList.remove('stop');
                    } else if (data.is_scanning) {
                        scanStatus.textContent = 'Scanning...';
                        scanButton.textContent = 'Stop Scan';
                        scanButton.classList.add('stop');
                    }
                    updateLedButton(data.led_state);
                }
                
                if (isFirstFetch) {
                    document.getElementById('calib-gain').value = data.calib_gain;
                    document.getElementById('calib-ir').value = data.calib_ir_comp;
                    document.getElementById('calib-norm').value = data.calib_norm;
                    updateLedButton(data.led_state);
                    isFirstFetch = false;
                }
            }

            function fetchData() {
                fetch('/fulldata')
                    .then(response => response.json())
                    .then(data => updateUI(data))
                    .catch(error => console.error('Error fetching data:', error));
            }

            fetchData();
            setInterval(fetchData, 1500);
        });
    </script>
</body>
</html>

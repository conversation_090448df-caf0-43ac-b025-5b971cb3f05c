# TCS3430 Sensor Troubleshooting Guide

## Current Issue: Invalid/NaN XYZ Data

You're seeing this error repeatedly:
```
[WARN ] [Sensor] Invalid or NaN XYZ data, skipping update.
```

This indicates the sensor is not providing valid data. Here's how to diagnose and fix it:

## Step 1: Hardware Check

### Wiring Verification
Double-check these connections:

**ESP32-S3 to TCS3430:**
- ESP32 Pin 8 (SDA) → TCS3430 SDA
- ESP32 Pin 9 (SCL) → TCS3430 SCL  
- ESP32 3.3V → TCS3430 VCC
- ESP32 GND → TCS3430 GND
- ESP32 Pin 21 → TCS3430 INT (optional)

### Pull-up Resistors
**CRITICAL**: I2C requires pull-up resistors!
- Add 4.7kΩ resistors from SDA to 3.3V
- Add 4.7kΩ resistors from SCL to 3.3V

Many TCS3430 breakout boards have these built-in, but verify yours does.

### Power Supply Check
- Measure voltage at TCS3430 VCC pin: should be 3.3V ±0.1V
- Check for stable power (no voltage drops during operation)
- Ensure adequate current supply (sensor draws ~5mA, LED ~15mA)

## Step 2: I2C Communication Test

### Upload Test Code
Upload the improved code and check the serial monitor for:

1. **Initialization Messages:**
   ```
   [INFO ] [Sensor] Attempting TCS3430 initialization...
   [INFO ] [Sensor] TCS3430 communication established.
   [INFO ] [Sensor] Sensor status: 0x??
   ```

2. **Health Check Messages:**
   ```
   [INFO ] [Health] Sensor Health - Status:0x??, X:??, Y:??, Z:??
   ```

### Expected vs Problem Indicators

**Good Communication:**
- Status values like 0x01, 0x02, 0x04, etc.
- X, Y, Z values > 0 (even in darkness should be small positive numbers)

**Communication Problems:**
- Status = 0xFF or 0x00 consistently
- All X, Y, Z = 0
- NaN values

## Step 3: I2C Scanner Test

Add this simple I2C scanner to verify the sensor address:

```cpp
void scanI2C() {
  Serial.println("Scanning I2C bus...");
  for (byte addr = 1; addr < 127; addr++) {
    Wire.beginTransmission(addr);
    if (Wire.endTransmission() == 0) {
      Serial.print("Device found at 0x");
      Serial.println(addr, HEX);
    }
  }
}
```

**Expected Result:** Should find device at 0x39 (TCS3430 default address)

## Step 4: Common Issues and Solutions

### Issue 1: No I2C Device Found
**Symptoms:** I2C scanner finds no devices
**Solutions:**
1. Check wiring (especially SDA/SCL swap)
2. Verify pull-up resistors
3. Check power connections
4. Try different I2C pins
5. Test with multimeter for continuity

### Issue 2: Wrong I2C Address
**Symptoms:** Device found at different address
**Solutions:**
1. Some boards may have address jumpers
2. Check board documentation
3. Modify code to use correct address

### Issue 3: Intermittent Communication
**Symptoms:** Sometimes works, sometimes fails
**Solutions:**
1. Check for loose connections
2. Add decoupling capacitors (100nF ceramic + 10µF electrolytic)
3. Reduce I2C clock speed (already set to 100kHz in improved code)
4. Check for electromagnetic interference

### Issue 4: Sensor Responds but Returns Zeros
**Symptoms:** Status OK, but X=Y=Z=0
**Solutions:**
1. Check if sensor is covered/in darkness
2. Turn on LED (Pin 5)
3. Verify integration time settings
4. Check gain settings

## Step 5: Hardware Testing Steps

### Test 1: Basic I2C Communication
1. Upload improved code
2. Open Serial Monitor (115200 baud)
3. Look for initialization messages
4. Check for health check reports

### Test 2: LED Test
1. Use web interface to toggle LED
2. LED should turn on/off on Pin 5
3. If LED doesn't work, check Pin 5 connection

### Test 3: Manual Sensor Reading
Add this to your loop() for testing:

```cpp
// Manual sensor test
static uint32_t last_manual_test = 0;
if (millis() - last_manual_test > 2000) {
  uint16_t raw_x = tcs3430_sensor.getXData();
  uint16_t raw_y = tcs3430_sensor.getYData();
  uint16_t raw_z = tcs3430_sensor.getZData();
  uint8_t status = tcs3430_sensor.getDeviceStatus();
  
  Serial.printf("Manual Test - Status:0x%02X, X:%d, Y:%d, Z:%d\n", 
                status, raw_x, raw_y, raw_z);
  last_manual_test = millis();
}
```

## Step 6: Alternative Sensor Testing

If you have access to another TCS3430 sensor or breakout board, try swapping it to isolate hardware vs software issues.

## Step 7: Breadboard vs PCB

If using a breadboard:
- Check for loose connections
- Try different breadboard sections
- Consider soldering connections for testing

## Expected Serial Output After Fix

You should see:
```
[INFO ] [Sensor] TCS3430 initialized successfully.
[INFO ] [Calibration] Enhanced sensor configuration applied successfully.
[INFO ] [Health] Sensor Health - Status:0x01, X:150, Y:200, Z:100
[DEBUG] [Sensor] Raw XYZ: X=150.0, Y=200.0, Z=100.0, IR1=50, IR2=45
```

Instead of:
```
[WARN ] [Sensor] Invalid or NaN XYZ data, skipping update.
```

## Next Steps

1. **Upload the improved code** with enhanced diagnostics
2. **Check serial monitor** for detailed error messages
3. **Verify hardware connections** especially I2C and power
4. **Test I2C communication** with scanner
5. **Report findings** - what specific error messages do you see?

The improved code will give us much better diagnostic information to pinpoint the exact issue!
